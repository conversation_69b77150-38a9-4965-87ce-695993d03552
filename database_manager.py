#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据库操作模块
"""

import sqlite3
import os
import csv
from typing import Dict, List, Optional, Tuple, Union
from pathlib import Path

from config import Config
from logger import get_logger

class DatabaseManager:
    """数据库管理器"""
    
    def __init__(self, config: Config):
        self.config = config
        self.logger = get_logger()
        self.db_path = config.db_path
        self.init_database()
    
    def init_database(self):
        """初始化数据库"""
        try:
            # 如果数据库文件不存在，创建它
            if not os.path.exists(self.db_path):
                self.logger.info(f"创建新数据库: {self.db_path}")
            
            with sqlite3.connect(self.db_path) as conn:
                # 读取SQL文件并执行
                sql_file = "雅思单词数据库设计.sql"
                if os.path.exists(sql_file):
                    with open(sql_file, 'r', encoding='utf-8') as f:
                        sql_script = f.read()
                    conn.executescript(sql_script)
                    self.logger.info("使用SQL文件初始化数据库")
                else:
                    # 如果SQL文件不存在，使用基本的表结构
                    self._create_basic_table(conn)
                    self.logger.info("使用基本表结构初始化数据库")
                
                conn.commit()
                self.logger.info("数据库初始化完成")
                
        except Exception as e:
            self.logger.error(f"数据库初始化失败: {e}")
            raise
    
    def _create_basic_table(self, conn):
        """创建基本表结构"""
        create_table_sql = """
        CREATE TABLE IF NOT EXISTS ielts_words (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            unit_name TEXT NOT NULL,
            word TEXT NOT NULL,
            phonetic TEXT NOT NULL,
            part_of_speech TEXT NOT NULL,
            meaning TEXT NOT NULL,
            root_affix TEXT NOT NULL,
            contained_words TEXT,
            phrases TEXT NOT NULL,
            examples TEXT NOT NULL,
            synonyms_antonyms TEXT NOT NULL,
            ielts_synonyms TEXT NOT NULL,
            memory_aid TEXT NOT NULL,
            common_scenarios TEXT NOT NULL,
            difficulty TEXT NOT NULL CHECK (
                difficulty IN (
                    '基础(Basic)',
                    '进阶(Intermediate)',
                    '高阶(Advanced)',
                    '专家(Expert)'
                )
            ),
            theme TEXT NOT NULL CHECK (
                theme IN (
                    '商务与职场 (Business & Workplace)',
                    '学术与生活 (Academics & Campus Life)',
                    '新闻与公共事务 (News & Public Affairs)',
                    '自然与生物 (Nature & Biology)',
                    '健康与医疗 (Health & Medical)',
                    '日常生活与家居 (Daily Life & Household)',
                    '社交与人际 (Social & Interpersonal)',
                    '抽象描述与通用词 (Abstract Descriptions & General Words)',
                    '技术、艺术与娱乐 (Tech, Arts & Entertainment)'
                )
            ),
            ielts_frequency TEXT NOT NULL CHECK (
                ielts_frequency IN (
                    '高频(High Frequency)',
                    '中频(Medium Frequency)',
                    '低频(Low Frequency)'
                )
            ),
            learning_status TEXT NOT NULL DEFAULT '待学习 (To Learn)' CHECK (
                learning_status IN (
                    '待学习 (To Learn)',
                    '进行中 (In Progress)',
                    '已掌握 (Learned)',
                    '长期记忆 (Long-term Memory)'
                )
            ),
            learning_tags TEXT NOT NULL,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            UNIQUE(unit_name, word)
        );
        """
        conn.execute(create_table_sql)
        
        # 创建索引
        indexes = [
            "CREATE INDEX IF NOT EXISTS idx_word ON ielts_words(word);",
            "CREATE INDEX IF NOT EXISTS idx_unit_name ON ielts_words(unit_name);",
            "CREATE INDEX IF NOT EXISTS idx_word_unit ON ielts_words(word, unit_name);"
        ]
        
        for index_sql in indexes:
            conn.execute(index_sql)
    
    def word_exists(self, unit_name: str, word: str) -> bool:
        """检查单词是否已存在"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.execute(
                    "SELECT COUNT(*) FROM ielts_words WHERE unit_name = ? AND word = ?",
                    (unit_name, word)
                )
                return cursor.fetchone()[0] > 0
        except Exception as e:
            self.logger.error(f"检查单词存在性失败: {e}")
            return False
    
    def insert_word(self, unit_name: str, word_data: Dict) -> Tuple[bool, Optional[str]]:
        """插入单词数据，返回(成功状态, 错误信息)"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                insert_sql = """
                INSERT OR REPLACE INTO ielts_words (
                    unit_name, word, phonetic, part_of_speech, meaning,
                    root_affix, contained_words, phrases, examples,
                    synonyms_antonyms, ielts_synonyms, memory_aid,
                    common_scenarios, difficulty, theme, ielts_frequency,
                    learning_status, learning_tags
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                """

                values = (
                    unit_name,
                    word_data.get('word', ''),
                    word_data.get('phonetic', ''),
                    word_data.get('part_of_speech', ''),
                    word_data.get('meaning', ''),
                    word_data.get('root_affix', ''),
                    word_data.get('contained_words', ''),
                    word_data.get('phrases', ''),
                    word_data.get('examples', ''),
                    word_data.get('synonyms_antonyms', ''),
                    word_data.get('ielts_synonyms', ''),
                    word_data.get('memory_aid', ''),
                    word_data.get('common_scenarios', ''),
                    word_data.get('difficulty', ''),
                    word_data.get('theme', ''),
                    word_data.get('ielts_frequency', ''),
                    '待学习 (To Learn)',  # 默认学习状态
                    word_data.get('learning_tags', '')
                )

                conn.execute(insert_sql, values)
                conn.commit()
                self.logger.info(f"成功插入单词: {word_data.get('word', 'unknown')}")
                return True, None

        except sqlite3.IntegrityError as e:
            error_msg = str(e)
            word = word_data.get('word', 'unknown')

            # 解析具体的约束错误
            if "CHECK constraint failed: difficulty" in error_msg:
                current_value = word_data.get('difficulty', '')
                detailed_error = f"difficulty字段值'{current_value}'不符合约束。必须是以下之一：基础(Basic), 进阶(Intermediate), 高阶(Advanced), 专家(Expert)"
            elif "CHECK constraint failed: theme" in error_msg:
                current_value = word_data.get('theme', '')
                detailed_error = f"theme字段值'{current_value}'不符合约束。必须是以下之一：商务与职场 (Business & Workplace), 学术与生活 (Academics & Campus Life), 新闻与公共事务 (News & Public Affairs), 自然与生物 (Nature & Biology), 健康与医疗 (Health & Medical), 日常生活与家居 (Daily Life & Household), 社交与人际 (Social & Interpersonal), 抽象描述与通用词 (Abstract Descriptions & General Words), 技术、艺术与娱乐 (Tech, Arts & Entertainment)"
            elif "CHECK constraint failed: ielts_frequency" in error_msg:
                current_value = word_data.get('ielts_frequency', '')
                detailed_error = f"ielts_frequency字段值'{current_value}'不符合约束。必须是以下之一：高频(High Frequency), 中频(Medium Frequency), 低频(Low Frequency)"
            elif "learning_tags" in error_msg:
                current_value = word_data.get('learning_tags', '')
                detailed_error = f"learning_tags字段值'{current_value}'不符合约束。必须是以下选项的组合（用英文逗号分隔）：写作, 听力, 口语, 阅读。示例：写作,口语 或 听力,阅读,写作"
            else:
                # 检查是否是格式问题
                part_of_speech = word_data.get('part_of_speech', '')
                common_scenarios = word_data.get('common_scenarios', '')

                if '/' in part_of_speech:
                    detailed_error = f"词性格式错误: '{part_of_speech}'，应使用英文逗号分隔，如：adj.,v.,n."
                elif ';' in common_scenarios:
                    detailed_error = f"常见场景格式错误: '{common_scenarios}'，应使用英文逗号分隔，如：学术,科技,商务"
                else:
                    detailed_error = f"数据约束检查失败: {error_msg}"

            self.logger.error(f"数据约束检查失败 {word}: {detailed_error}")
            return False, detailed_error

        except Exception as e:
            error_msg = f"插入单词数据失败: {str(e)}"
            self.logger.error(f"{error_msg} {word_data.get('word', 'unknown')}")
            return False, error_msg
    
    def get_unit_statistics(self, unit_name: str) -> Dict:
        """获取单元统计信息"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.execute("""
                    SELECT 
                        COUNT(*) as total_words,
                        COUNT(CASE WHEN learning_status = '已掌握 (Learned)' THEN 1 END) as learned_words,
                        COUNT(CASE WHEN learning_status = '长期记忆 (Long-term Memory)' THEN 1 END) as memorized_words,
                        COUNT(CASE WHEN learning_tags = '必背' THEN 1 END) as must_learn_words
                    FROM ielts_words 
                    WHERE unit_name = ?
                """, (unit_name,))
                
                result = cursor.fetchone()
                if result:
                    total, learned, memorized, must_learn = result
                    completion_rate = ((learned + memorized) / total * 100) if total > 0 else 0
                    
                    return {
                        'unit_name': unit_name,
                        'total_words': total,
                        'learned_words': learned,
                        'memorized_words': memorized,
                        'must_learn_words': must_learn,
                        'completion_rate': round(completion_rate, 2)
                    }
                else:
                    return {
                        'unit_name': unit_name,
                        'total_words': 0,
                        'learned_words': 0,
                        'memorized_words': 0,
                        'must_learn_words': 0,
                        'completion_rate': 0.0
                    }
                    
        except Exception as e:
            self.logger.error(f"获取单元统计信息失败: {e}")
            return {}
    
    def get_overall_statistics(self) -> Dict:
        """获取总体统计信息"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.execute("SELECT COUNT(*) FROM ielts_words")
                total_words = cursor.fetchone()[0]
                
                cursor = conn.execute("SELECT COUNT(DISTINCT unit_name) FROM ielts_words")
                total_units = cursor.fetchone()[0]
                
                cursor = conn.execute("""
                    SELECT 
                        COUNT(CASE WHEN learning_status = '已掌握 (Learned)' THEN 1 END) as learned,
                        COUNT(CASE WHEN learning_status = '长期记忆 (Long-term Memory)' THEN 1 END) as memorized
                    FROM ielts_words
                """)
                learned, memorized = cursor.fetchone()
                
                return {
                    'total_words': total_words,
                    'total_units': total_units,
                    'learned_words': learned,
                    'memorized_words': memorized,
                    'completion_rate': round(((learned + memorized) / total_words * 100) if total_words > 0 else 0, 2)
                }
                
        except Exception as e:
            self.logger.error(f"获取总体统计信息失败: {e}")
            return {'total_words': 0, 'total_units': 0, 'learned_words': 0, 'memorized_words': 0, 'completion_rate': 0.0}
    
    def get_processed_words(self, unit_name: str) -> List[str]:
        """获取已处理的单词列表"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.execute(
                    "SELECT word FROM ielts_words WHERE unit_name = ?",
                    (unit_name,)
                )
                return [row[0] for row in cursor.fetchall()]
        except Exception as e:
            self.logger.error(f"获取已处理单词列表失败: {e}")
            return []

    def export_to_csv(self, csv_file_path: str = "ielts_words_export.csv") -> bool:
        """导出所有单词数据到CSV文件"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.execute("""
                    SELECT
                        unit_name, word, phonetic, part_of_speech, meaning,
                        root_affix, contained_words, phrases, examples,
                        synonyms_antonyms, ielts_synonyms, memory_aid,
                        common_scenarios, difficulty, theme, ielts_frequency,
                        learning_status, learning_tags, created_at, updated_at
                    FROM ielts_words
                    ORDER BY CAST(unit_name AS INTEGER), word
                """)

                # 获取列名
                column_names = [
                    '单元', '单词', '音标', '词性', '含义',
                    '词根词缀', '内含单词', '词组', '例句',
                    '近反义词', '雅思同义词', '联想记忆',
                    '常见场景', '难度', '主题', '雅思频率',
                    '学习状态', '学习标签', '创建时间', '更新时间'
                ]

                # 写入CSV文件
                with open(csv_file_path, 'w', newline='', encoding='utf-8-sig') as csvfile:
                    writer = csv.writer(csvfile)

                    # 写入表头
                    writer.writerow(column_names)

                    # 写入数据
                    rows = cursor.fetchall()
                    for row in rows:
                        writer.writerow(row)

                self.logger.info(f"成功导出 {len(rows)} 条记录到 {csv_file_path}")
                return True

        except Exception as e:
            self.logger.error(f"导出CSV文件失败: {e}")
            return False

    def get_export_statistics(self) -> Dict:
        """获取导出统计信息"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.execute("""
                    SELECT
                        unit_name,
                        COUNT(*) as word_count
                    FROM ielts_words
                    GROUP BY unit_name
                    ORDER BY CAST(unit_name AS INTEGER)
                """)

                units_data = cursor.fetchall()

                cursor = conn.execute("SELECT COUNT(*) FROM ielts_words")
                total_words = cursor.fetchone()[0]

                return {
                    'total_words': total_words,
                    'units': [{'unit_name': unit, 'word_count': count} for unit, count in units_data]
                }

        except Exception as e:
            self.logger.error(f"获取导出统计信息失败: {e}")
            return {'total_words': 0, 'units': []}
