#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
配置管理模块
"""

import os
from dataclasses import dataclass
from typing import Optional

@dataclass
class Config:
    """配置类"""
    
    # AI模型配置
    api_key: str = "sk-e2a07e07cdcc4bb3bf645feda1f1c265"
    base_url: str = "https://dashscope.aliyuncs.com/compatible-mode/v1"
    model_name: str = "qwen-plus"
    
    # 数据库配置
    db_path: str = "ielts_words.db"
    
    # 处理配置
    request_delay: float = 1.0  # 请求间隔（秒）
    max_retries: int = 6  # 最大重试次数
    timeout: int = 30  # 请求超时时间
    

    
    # 日志配置
    log_file: str = "ielts_word_processor.log"
    log_level: str = "INFO"
    
    # 输入文件配置
    words_file_pattern: str = "words{}.txt"  # 单词文件名模式
    
    @classmethod
    def from_env(cls) -> 'Config':
        """从环境变量创建配置"""
        return cls(
            api_key=os.getenv('DASHSCOPE_API_KEY', cls.api_key),
            base_url=os.getenv('DASHSCOPE_BASE_URL', cls.base_url),
            model_name=os.getenv('MODEL_NAME', cls.model_name),
            db_path=os.getenv('DB_PATH', cls.db_path),
            request_delay=float(os.getenv('REQUEST_DELAY', cls.request_delay)),
            max_retries=int(os.getenv('MAX_RETRIES', cls.max_retries)),
            timeout=int(os.getenv('TIMEOUT', cls.timeout)),

            log_file=os.getenv('LOG_FILE', cls.log_file),
            log_level=os.getenv('LOG_LEVEL', cls.log_level),
            words_file_pattern=os.getenv('WORDS_FILE_PATTERN', cls.words_file_pattern)
        )
    
    def validate(self) -> bool:
        """验证配置"""
        if not self.api_key:
            raise ValueError("API密钥不能为空")
        if not self.base_url:
            raise ValueError("API基础URL不能为空")
        if not self.model_name:
            raise ValueError("模型名称不能为空")
        if self.max_retries < 1:
            raise ValueError("最大重试次数必须大于0")
        if self.timeout < 1:
            raise ValueError("超时时间必须大于0")
        if self.request_delay < 0:
            raise ValueError("请求延迟不能为负数")
        return True
