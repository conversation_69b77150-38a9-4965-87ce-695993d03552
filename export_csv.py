#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
雅思单词数据CSV导出工具
"""

import sys
import time
from config import Config
from database_manager import DatabaseManager
from logger import get_logger

def main():
    """主函数"""
    try:
        # 加载配置
        config = Config.from_env()
        
        # 初始化日志
        logger = get_logger(config.log_file, "INFO")
        
        # 创建数据库管理器
        db_manager = DatabaseManager(config)
        
        # 获取统计信息
        stats = db_manager.get_overall_statistics()
        
        if stats['total_words'] == 0:
            print("数据库中没有单词数据，无法导出")
            return 1
        
        print(f"数据库统计:")
        print(f"  总单词数: {stats['total_words']}")
        print(f"  总单元数: {stats['total_units']}")
        print(f"  学习完成率: {stats['completion_rate']}%")
        print()
        
        # 获取详细统计
        export_stats = db_manager.get_export_statistics()
        print("各单元统计:")
        for unit_info in export_stats['units']:
            print(f"  单元 {unit_info['unit_name']}: {unit_info['word_count']} 个单词")
        print()
        
        # 生成CSV文件名
        if len(sys.argv) > 1:
            csv_filename = sys.argv[1]
        else:
            csv_filename = f"ielts_words_export_{time.strftime('%Y%m%d_%H%M%S')}.csv"
        
        print(f"正在导出到文件: {csv_filename}")
        
        # 导出CSV
        if db_manager.export_to_csv(csv_filename):
            print(f"✓ 成功导出 {stats['total_words']} 个单词到 {csv_filename}")
            print()
            print("CSV文件包含以下字段:")
            print("  单元, 单词, 音标, 词性, 含义, 词根词缀, 内含单词, 词组, 例句,")
            print("  近反义词, 雅思同义词, 联想记忆, 常见场景, 难度, 主题, 雅思频率,")
            print("  学习状态, 学习标签, 创建时间, 更新时间")
            return 0
        else:
            print("✗ 导出失败，请查看日志文件")
            return 1
            
    except Exception as e:
        print(f"导出过程中发生错误: {e}")
        return 1

if __name__ == "__main__":
    print("=" * 50)
    print("雅思单词数据CSV导出工具")
    print("=" * 50)
    
    if len(sys.argv) > 1:
        print(f"导出文件名: {sys.argv[1]}")
    else:
        print("将使用默认文件名（包含时间戳）")
    
    print()
    exit_code = main()
    
    if exit_code == 0:
        print("\n导出完成！")
    else:
        print("\n导出失败！")
    
    sys.exit(exit_code)
