#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
日志管理模块
"""

import logging
import sys
from pathlib import Path
from typing import Optional

class Logger:
    """日志管理器"""
    
    def __init__(self, log_file: str = "ielts_word_processor.log", log_level: str = "INFO"):
        self.log_file = log_file
        self.log_level = getattr(logging, log_level.upper(), logging.INFO)
        # 确保DEBUG级别的日志也能显示
        if log_level.upper() == "DEBUG":
            self.log_level = logging.DEBUG
        self.logger = self._setup_logger()
    
    def _setup_logger(self) -> logging.Logger:
        """设置日志记录器"""
        logger = logging.getLogger("ielts_word_processor")
        logger.setLevel(self.log_level)
        
        # 避免重复添加处理器
        if logger.handlers:
            return logger
        
        # 创建格式化器
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )
        
        # 文件处理器
        file_handler = logging.FileHandler(self.log_file, encoding='utf-8')
        file_handler.setLevel(self.log_level)
        file_handler.setFormatter(formatter)
        logger.addHandler(file_handler)
        
        # 控制台处理器
        console_handler = logging.StreamHandler(sys.stdout)
        console_handler.setLevel(self.log_level)
        console_handler.setFormatter(formatter)
        logger.addHandler(console_handler)
        
        return logger
    
    def info(self, message: str):
        """记录信息日志"""
        self.logger.info(message)
    
    def warning(self, message: str):
        """记录警告日志"""
        self.logger.warning(message)
    
    def error(self, message: str):
        """记录错误日志"""
        self.logger.error(message)
    
    def debug(self, message: str):
        """记录调试日志"""
        self.logger.debug(message)
    
    def exception(self, message: str):
        """记录异常日志"""
        self.logger.exception(message)

# 全局日志实例
_logger_instance: Optional[Logger] = None

def get_logger(log_file: str = "ielts_word_processor.log", log_level: str = "INFO") -> Logger:
    """获取日志实例（单例模式）"""
    global _logger_instance
    if _logger_instance is None:
        _logger_instance = Logger(log_file, log_level)
    return _logger_instance
