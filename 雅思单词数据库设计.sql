-- 雅思单词学习系统数据库设计
-- SQLite数据库表结构
-- 创建雅思单词表
CREATE TABLE IF NOT EXISTS ielts_words (
    -- 主键和基础信息
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    unit_name TEXT NOT NULL,
    -- 单元名称（如：1、2）
    word TEXT NOT NULL,
    -- 单词原文
    phonetic TEXT NOT NULL,
    -- 音标
    part_of_speech TEXT NOT NULL,
    -- 词性
    meaning TEXT NOT NULL,
    -- 含义
    -- 词汇分析字段
    root_affix TEXT NOT NULL,
    -- 词根词缀
    contained_words TEXT,
    -- 内含单词（可为空）
    phrases TEXT NOT NULL,
    -- 词组
    examples TEXT NOT NULL,
    -- 例句
    -- 同义词反义词字段
    synonyms_antonyms TEXT NOT NULL,
    -- 近反义词
    ielts_synonyms TEXT NOT NULL,
    -- 雅思同义词
    memory_aid TEXT NOT NULL,
    -- 联想记忆
    -- 分类字段（带约束）
    common_scenarios TEXT NOT NULL,
    -- 常见场景
    difficulty TEXT NOT NULL CHECK (
        difficulty IN (
            '基础(Basic)',
            '进阶(Intermediate)',
            '高阶(Advanced)',
            '专家(Expert)'
        )
    ),
    theme TEXT NOT NULL CHECK (
        theme IN (
            '商务与职场 (Business & Workplace)',
            '学术与生活 (Academics & Campus Life)',
            '新闻与公共事务 (News & Public Affairs)',
            '自然与生物 (Nature & Biology)',
            '健康与医疗 (Health & Medical)',
            '日常生活与家居 (Daily Life & Household)',
            '社交与人际 (Social & Interpersonal)',
            '抽象描述与通用词 (Abstract Descriptions & General Words)',
            '技术、艺术与娱乐 (Tech, Arts & Entertainment)'
        )
    ),
    ielts_frequency TEXT NOT NULL CHECK (
        ielts_frequency IN (
            '高频(High Frequency)',
            '中频(Medium Frequency)',
            '低频(Low Frequency)'
        )
    ),
    learning_status TEXT NOT NULL DEFAULT '待学习 (To Learn)' CHECK (
        learning_status IN (
            '待学习 (To Learn)',
            '进行中 (In Progress)',
            '已掌握 (Learned)',
            '长期记忆 (Long-term Memory)'
        )
    ),
    learning_tags TEXT NOT NULL,
    -- 学习标签：写作,听力,口语,阅读（多选，用英文逗号分隔）
    -- 时间戳字段
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    -- 唯一约束：同一单元内单词不能重复
    UNIQUE(unit_name, word)
);