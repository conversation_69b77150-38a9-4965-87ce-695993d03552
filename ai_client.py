#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI模型调用模块
"""

import json
import re
import time
from typing import Dict, Optional
import requests
from requests.adapters import HTTPAdapter
from urllib3.util.retry import Retry

from config import Config
from logger import get_logger

class AIClient:
    """AI模型客户端"""
    
    def __init__(self, config: Config):
        self.config = config
        self.logger = get_logger()
        self.session = self._create_session()
        self.system_prompt = self._load_system_prompt()
    
    def _create_session(self) -> requests.Session:
        """创建HTTP会话"""
        session = requests.Session()
        
        # 配置重试策略
        retry_strategy = Retry(
            total=3,
            backoff_factor=1,
            status_forcelist=[429, 500, 502, 503, 504],
        )
        adapter = HTTPAdapter(max_retries=retry_strategy)
        session.mount("http://", adapter)
        session.mount("https://", adapter)
        
        return session
    
    def _load_system_prompt(self) -> str:
        return self._get_default_prompt()
        """
        try:
            with open('雅思单词AI提示词.md', 'r', encoding='utf-8') as f:
                content = f.read()
                
            # 提取系统提示词部分
            start_marker = "## 系统提示词"
            end_marker = "## JSON格式模板"
            start_idx = content.find(start_marker)
            end_idx = content.find(end_marker)
            
            if start_idx != -1 and end_idx != -1:
                prompt = content[start_idx + len(start_marker):end_idx].strip()
                self.logger.info("成功加载系统提示词")
                return prompt
            else:
                self.logger.warning("未找到系统提示词标记，使用默认提示词")
                return self._get_default_prompt()
                
        except Exception as e:
            self.logger.error(f"加载系统提示词失败: {e}")
            return self._get_default_prompt()
        """
    def _get_default_prompt(self) -> str:
        """获取默认提示词"""
        return """你是一个专业的雅思单词分析专家。请根据给定的英文单词，返回严格按照以下JSON格式的完整单词信息。

**重要要求：**
1. 必须返回有效的JSON格式，不要包含任何其他文字说明
2. 所有字段都必须填写，不能为空
3. 严格按照指定的选项范围填写限定字段
4. 中英文内容格式必须完全一致

## JSON格式模板

```json
{
  "word": "单词原文",
  "phonetic": "/音标/",
  "part_of_speech": "词性（如：adj.,v.,n.等）",
  "meaning": "详细的中文含义解释",
  "root_affix": "词根词缀分析（格式：前缀/词根/后缀 + 含义解释）",
  "contained_words": "内含单词(中文含义);内含单词2(中文含义)",
  "phrases": "词组1(中文含义);词组2(中文含义)",
  "examples": "英文例句1(中文翻译);英文例句2(中文翻译)",
  "synonyms_antonyms": "同义词1(中文);同义词2(中文)/反义词1(中文);反义词2(中文)",
  "ielts_synonyms": "雅思同义词1(中文);雅思同义词2(中文);雅思同义词3(中文)",
  "memory_aid": "联想记忆方法或技巧",
  "common_scenarios": "场景1,场景2",
  "difficulty": "难度等级",
  "theme": "单词主题",
  "ielts_frequency": "雅思频率",
  "learning_tags": "学习标签"
}
```

## 字段详细要求

### 基础信息字段
- **word**: 输入的英文单词原文
- **phonetic**: 国际音标，格式：/ɪɡˈzækt/
- **part_of_speech**: 词性缩写，用英文逗号分隔，如：adj.,v.,n.,adv.等
- **meaning**: 详细的中文含义，包含所有主要词性的解释

### 词汇分析字段
- **root_affix**: 词根词缀分析，格式："前缀(含义) + 词根(含义) + 后缀(含义) = 整体含义"
- **contained_words**: 内含单词，格式："单词1(中文含义);单词2(中文含义)"，如果没有则填"无"
- **phrases**: 两个常用词组，格式："词组1(中文含义);词组2(中文含义)"
- **examples**: 两个例句，格式："英文例句1(中文翻译);英文例句2(中文翻译)"

### 同义词反义词字段
- **synonyms_antonyms**: 格式："同义词1(中文);同义词2(中文)/反义词1(中文);反义词2(中文)"
- **ielts_synonyms**: 雅思同义词，格式："同义词1(中文);同义词2(中文);同义词3(中文)"
- **memory_aid**: 联想记忆方法，提供具体的记忆技巧

### 分类字段（必须从指定选项中选择）

#### common_scenarios（常见场景）- 可多选，用英文逗号分隔
选项：学术,商务,社交,日常生活,历史,法律,科技,文化,经济等

#### difficulty（难度等级）- 单选
选项：基础(Basic)|进阶(Intermediate)|高阶(Advanced)|专家(Expert)
判断标准：
- 基础：日常高频，中学词汇，词义单一（如book, happy, travel）
- 进阶：学术书面常用，阅读写作高频，比基础词更精准（如significant, analyze）
- 高阶：抽象书面化，多学科领域，搭配复杂（如mitigate, hierarchy）
- 专家：学术专业性极强，正式罕见（如epistemology, dichotomy）

#### theme（单词主题）- 单选
选项：
- 商务与职场 (Business & Workplace)
- 学术与生活 (Academics & Campus Life)
- 新闻与公共事务 (News & Public Affairs)
- 自然与生物 (Nature & Biology)
- 健康与医疗 (Health & Medical)
- 日常生活与家居 (Daily Life & Household)
- 社交与人际 (Social & Interpersonal)
- 抽象描述与通用词 (Abstract Descriptions & General Words)
- 技术、艺术与娱乐 (Tech, Arts & Entertainment)
必须从以上主题中选择，完全一致，不要自己编造，历史和文化类的主题请归为学术与生活,经济类的主题请归为商务与职场

#### ielts_frequency（雅思频率）- 单选
选项：高频(High Frequency)|中频(Medium Frequency)|低频(Low Frequency)
必须从以上频率中选择，完全一致，不要自己编造

#### learning_tags（学习标签）- 多选，用英文逗号分隔
选项：写作,听力,口语,阅读,请一定要会完整拼写(五个选项)
说明：标识该单词在雅思考试的哪些题型中需要掌握
示例：写作,口语,请一定要会完整拼写 或 听力,阅读,写作
必须从以上标签中选择，完全一致，不要自己编造

## 示例输出

输入单词：exact

```json
{
  "word": "exact",
  "phonetic": "/ɪɡˈzækt/",
  "part_of_speech": "adj.,v.",
  "meaning": "adj. 精确的，准确的；严格的 v. 要求，索取",
  "root_affix": "ex-(出，外) + act(做，行动) = 做出来的→精确的",
  "contained_words": "act(行动)",
  "phrases": "exact science(精密科学);exact change(准确找零)",
  "examples": "Please give me the exact time.(请告诉我准确时间。);The experiment requires exact measurements.(实验需要精确的测量。)",
  "synonyms_antonyms": "precise(精确的);accurate(准确的)/approximate(大约的);rough(粗糙的)",
  "ielts_synonyms": "precise(精确的);accurate(准确的);specific(具体的)",
  "memory_aid": "ex(出来) + act(行动) = 行动要做出来→要精确",
  "common_scenarios": "学术,科技",
  "difficulty": "进阶(Intermediate)",
  "theme": "学术与生活 (Academics & Campus Life)",
  "ielts_frequency": "中频(Medium Frequency)",
  "learning_tags": "写作,口语"
}
```

## 质量检查要点

1. **JSON格式正确性**：确保返回的是有效的JSON，可以被程序正确解析
2. **字段完整性**：所有字段都必须存在且有内容
3. **格式一致性**：严格按照指定格式填写，特别是分隔符的使用
4. **选项准确性**：限定字段必须从指定选项中选择
5. **内容质量**：提供准确、实用的单词信息，特别是雅思考试相关的内容

请严格按照以上要求分析单词并返回JSON格式的结果。"""
    
    def _extract_json_from_response(self, content: str) -> Optional[Dict]:
        """从响应中提取JSON"""
        try:
            # 方法1: 尝试直接解析整个内容
            try:
                return json.loads(content.strip())
            except json.JSONDecodeError:
                pass
            
            # 方法2: 查找markdown代码块中的JSON
            json_match = re.search(r'```json\s*\n(.*?)\n```', content, re.DOTALL)
            if json_match:
                json_str = json_match.group(1).strip()
                return json.loads(json_str)
            
            # 方法3: 查找普通代码块中的JSON
            json_match = re.search(r'```\s*\n(.*?)\n```', content, re.DOTALL)
            if json_match:
                json_str = json_match.group(1).strip()
                if json_str.startswith('{') and json_str.endswith('}'):
                    return json.loads(json_str)
            
            # 方法4: 查找第一个完整的JSON对象
            json_start = content.find('{')
            if json_start != -1:
                brace_count = 0
                json_end = json_start
                for i, char in enumerate(content[json_start:], json_start):
                    if char == '{':
                        brace_count += 1
                    elif char == '}':
                        brace_count -= 1
                        if brace_count == 0:
                            json_end = i + 1
                            break
                
                if brace_count == 0:
                    json_str = content[json_start:json_end]
                    return json.loads(json_str)
            
            self.logger.error(f"无法从响应中提取JSON: {content[:200]}...")
            return None
            
        except json.JSONDecodeError as e:
            self.logger.error(f"JSON解析失败: {e}")
            return None
        except Exception as e:
            self.logger.error(f"提取JSON时发生错误: {e}")
            return None
    
    def _validate_word_data(self, word_data: Dict, expected_word: str) -> bool:
        """验证单词数据"""
        required_fields = [
            'word', 'phonetic', 'part_of_speech', 'meaning', 'root_affix',
            'phrases', 'examples', 'synonyms_antonyms', 'ielts_synonyms',
            'memory_aid', 'common_scenarios', 'difficulty', 'theme',
            'ielts_frequency', 'learning_tags'
        ]
        
        # 检查必需字段
        for field in required_fields:
            if field not in word_data or not word_data[field]:
                self.logger.error(f"缺少必需字段或字段为空: {field}")
                return False
        
        # 检查单词是否匹配
        if word_data['word'].lower() != expected_word.lower():
            self.logger.error(f"返回的单词不匹配: 期望 {expected_word}, 得到 {word_data['word']}")
            return False

        # 验证词性格式（应使用英文逗号分隔）
        part_of_speech = word_data['part_of_speech']
        if '/' in part_of_speech:
            self.logger.error(f"词性格式错误: {part_of_speech}，应使用英文逗号分隔，如：adj.,v.,n.")
            return False

        # 验证常见场景格式（应使用英文逗号分隔）
        common_scenarios = word_data['common_scenarios']
        if ';' in common_scenarios:
            self.logger.error(f"常见场景格式错误: {common_scenarios}，应使用英文逗号分隔，如：学术,科技,商务")
            return False

        # # 验证常见场景的有效性
        # valid_scenarios = ['学术', '商务', '社交', '日常生活', '历史', '法律', '科技', '文化', '经济']
        # scenarios = [s.strip() for s in common_scenarios.split(',')]
        # for scenario in scenarios:
        #     if scenario not in valid_scenarios:
        #         self.logger.error(f"常见场景无效: {scenario}，有效选项: {valid_scenarios}")
        #         return False
        
        # 检查限定字段的值
        valid_difficulties = ['基础(Basic)', '进阶(Intermediate)', '高阶(Advanced)', '专家(Expert)']
        if word_data['difficulty'] not in valid_difficulties:
            self.logger.error(f"难度等级无效: {word_data['difficulty']}")
            return False
        
        valid_frequencies = ['高频(High Frequency)', '中频(Medium Frequency)', '低频(Low Frequency)']
        if word_data['ielts_frequency'] not in valid_frequencies:
            self.logger.error(f"雅思频率无效: {word_data['ielts_frequency']}")
            return False
        
        # 验证学习标签（多选，用逗号分隔）
        valid_tags = ['写作', '听力', '口语', '阅读', '请一定要会完整拼写']
        learning_tags = word_data['learning_tags'].split(',')
        for tag in learning_tags:
            tag = tag.strip()
            if tag not in valid_tags:
                self.logger.error(f"学习标签无效: {tag}，有效选项: {valid_tags}")
                return False
        
        return True
    
    def get_word_info(self, word: str, error_context: str = None) -> Optional[Dict]:
        """获取单词信息，支持错误上下文重试"""
        for attempt in range(self.config.max_retries):
            try:
                self.logger.info(f"正在获取单词信息: {word} (尝试 {attempt + 1}/{self.config.max_retries})")

                # 构建用户消息，包含错误上下文
                user_message = f"请分析单词: {word}"
                if error_context and attempt > 0:
                    user_message += f"\n\n注意：上一次分析出现错误：{error_context}\n请确保返回的数据严格符合约束要求。"
                    self.logger.info(f"第{attempt + 1}次重试，包含错误上下文: {error_context}")

                headers = {
                    'Authorization': f'Bearer {self.config.api_key}',
                    'Content-Type': 'application/json'
                }

                data = {
                    "model": self.config.model_name,
                    "messages": [
                        {
                            "role": "system",
                            "content": self.system_prompt
                        },
                        {
                            "role": "user",
                            "content": user_message
                        }
                    ],
                    "temperature": 0.1,
                    "max_tokens": 2000,
                    "response_format": {"type": "json_object"}
                }

                response = self.session.post(
                    f"{self.config.base_url}/chat/completions",
                    headers=headers,
                    json=data,
                    timeout=self.config.timeout
                )

                if response.status_code == 200:
                    result = response.json()
                    content = result.get('choices', [{}])[0].get('message', {}).get('content', '')

                    if not content:
                        self.logger.error(f"API返回空内容: {word}")
                        continue

                    # 记录原始响应内容用于调试
                    self.logger.debug(f"AI原始响应内容 (尝试 {attempt + 1}): {content}")

                    # 提取JSON
                    word_data = self._extract_json_from_response(content)
                    if not word_data:
                        self.logger.error(f"无法提取JSON数据: {word}")
                        self.logger.error(f"原始响应内容: {content}")
                        continue



                    # 验证数据
                    if self._validate_word_data(word_data, word):
                        self.logger.info(f"成功获取单词信息: {word}")
                        return word_data
                    else:
                        self.logger.error(f"数据验证失败: {word} (尝试 {attempt + 1})")
                        continue

                else:
                    self.logger.error(f"API请求失败 {word}: {response.status_code} - {response.text}")

            except Exception as e:
                self.logger.error(f"获取单词信息时发生错误 {word} (尝试 {attempt + 1}): {e}")

            # 等待后重试
            if attempt < self.config.max_retries - 1:
                time.sleep(self.config.request_delay * (attempt + 1))

        self.logger.error(f"获取单词信息失败，已达到最大重试次数: {word}")
        return None
