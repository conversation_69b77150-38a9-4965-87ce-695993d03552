#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
单词提取模块
"""

import re
from pathlib import Path
from typing import List, Tuple, Optional
from logger import get_logger

class WordExtractor:
    """单词提取器"""
    
    def __init__(self):
        self.logger = get_logger()
    
    def extract_unit_from_filename(self, filename: str) -> Optional[str]:
        """从文件名中提取单元号"""
        try:
            # 匹配 words{数字}.txt 格式
            match = re.match(r'words(\d+)\.txt', filename)
            if match:
                unit_number = match.group(1)
                return unit_number
            else:
                self.logger.warning(f"文件名格式不正确: {filename}")
                return None
        except Exception as e:
            self.logger.error(f"提取单元号失败 {filename}: {e}")
            return None
    
    def extract_word_from_line(self, line: str) -> Optional[str]:
        """从行中提取单词"""
        try:
            # 去除首尾空白
            line = line.strip()
            if not line:
                return None
            
            # 匹配格式：数字. 单词/词性.含义
            # 例如：1. emperor/n.皇帝；君主
            match = re.match(r'^\d+\.\s*([a-zA-Z\-]+)', line)
            if match:
                word = match.group(1).strip()
                # 处理连字符单词，如 easy-going
                return word
            else:
                self.logger.warning(f"行格式不正确: {line}")
                return None
        except Exception as e:
            self.logger.error(f"提取单词失败 {line}: {e}")
            return None
    
    def load_words_from_file(self, file_path: str) -> Tuple[Optional[str], List[str]]:
        """从文件中加载单词列表"""
        try:
            path = Path(file_path)
            if not path.exists():
                self.logger.error(f"文件不存在: {file_path}")
                return None, []
            
            # 提取单元号
            unit_name = self.extract_unit_from_filename(path.name)
            if not unit_name:
                return None, []
            
            # 读取文件内容
            words = []
            with open(path, 'r', encoding='utf-8') as f:
                for line_num, line in enumerate(f, 1):
                    word = self.extract_word_from_line(line)
                    if word:
                        words.append(word)
                    elif line.strip():  # 非空行但提取失败
                        self.logger.warning(f"第{line_num}行单词提取失败: {line.strip()}")
            
            self.logger.info(f"从文件 {file_path} 提取到 {len(words)} 个单词，单元号: {unit_name}")
            return unit_name, words
            
        except Exception as e:
            self.logger.error(f"加载文件失败 {file_path}: {e}")
            return None, []
    
    def validate_word(self, word: str) -> bool:
        """验证单词格式"""
        if not word:
            return False
        
        # 检查是否只包含字母和连字符
        if not re.match(r'^[a-zA-Z\-]+$', word):
            return False
        
        # 检查长度
        if len(word) < 1 or len(word) > 50:
            return False
        
        return True
    
    def get_available_word_files(self, directory: str = ".") -> List[str]:
        """获取可用的单词文件列表"""
        try:
            path = Path(directory)
            word_files = []
            
            # 查找所有匹配 words{数字}.txt 格式的文件
            for file_path in path.glob("words*.txt"):
                if re.match(r'words\d+\.txt', file_path.name):
                    word_files.append(str(file_path))
            
            # 按文件名排序
            word_files.sort(key=lambda x: int(re.search(r'words(\d+)\.txt', Path(x).name).group(1)))
            
            self.logger.info(f"找到 {len(word_files)} 个单词文件")
            return word_files
            
        except Exception as e:
            self.logger.error(f"获取单词文件列表失败: {e}")
            return []
