# 雅思单词批量处理程序

这是一个用于批量处理雅思单词的Python程序，通过调用阿里云百炼大模型API获取单词的详细信息并存储到SQLite数据库中。

## 功能特性

- 🔤 **智能单词提取**：从格式化文本文件中自动提取英文单词
- 🤖 **AI驱动分析**：使用阿里云百炼大模型获取单词的详细信息
- 📊 **结构化存储**：将单词信息存储到SQLite数据库中
- 🔄 **断点续传**：基于数据库的进度管理，避免重复处理
- 📝 **详细日志**：完整的处理日志和错误记录
- 🛡️ **错误处理**：智能重试机制和数据验证
- 📊 **CSV导出**：支持将数据库中的单词数据导出为CSV格式

## 文件结构

```
├── main.py                    # 主程序入口
├── config.py                  # 配置管理模块
├── logger.py                  # 日志管理模块
├── word_extractor.py          # 单词提取模块
├── ai_client.py              # AI模型调用模块
├── database_manager.py        # 数据库操作模块
├── 雅思单词AI提示词.md        # AI提示词文档
├── 雅思单词数据库设计.sql     # 数据库设计文档
├── 模型调用文档.md            # API调用文档
├── export_csv.py             # CSV导出工具
├── words1.txt                # 单词文件示例
└── README.md                 # 使用说明
```

## 安装依赖

```bash
pip install requests
```

## 配置说明

程序支持通过环境变量或直接修改 `config.py` 文件来配置参数：

### 环境变量配置

```bash
export DASHSCOPE_API_KEY="your-api-key"
export MODEL_NAME="qwen3-235b-a22b-instruct-2507"
export DB_PATH="ielts_words.db"
export MAX_RETRIES="5"
export REQUEST_DELAY="1.0"
```

### 配置文件修改

直接编辑 `config.py` 文件中的 `Config` 类：

```python
@dataclass
class Config:
    api_key: str = "your-api-key"
    model_name: str = "qwen3-235b-a22b-instruct-2507"
    db_path: str = "ielts_words.db"
    max_retries: int = 5
    request_delay: float = 1.0
    # ... 其他配置
```

## 输入文件格式

单词文件需要遵循以下格式：

### 文件命名
- 格式：`words{数字}.txt`
- 示例：`words1.txt`, `words2.txt`, `words10.txt`
- 数字将作为数据库中的单元号

### 文件内容
每行一个单词，格式为：`序号. 单词/词性.中文含义`

```
1. emperor/n.皇帝；君主
2. exact/a.精确的；精准的
3. traditional/a.传统的，惯例
4. lack/n./vt.缺乏，不足
```

## 使用方法

### 1. 处理所有单词文件

```bash
python main.py
```

程序会自动查找当前目录下所有符合 `words*.txt` 格式的文件并处理。

### 2. 处理指定文件

```bash
python main.py words1.txt words2.txt
```

只处理指定的单词文件。

### 3. 查看处理进度

程序运行时会在控制台和日志文件中显示详细的处理进度：

```
2024-01-20 10:30:15 - ielts_word_processor - INFO - 开始处理文件: words1.txt
2024-01-20 10:30:15 - ielts_word_processor - INFO - 从文件 words1.txt 提取到 76 个单词，单元号: 1
2024-01-20 10:30:15 - ielts_word_processor - INFO - 单元 1: 总计 76 个单词，需要处理 76 个
2024-01-20 10:30:16 - ielts_word_processor - INFO - 处理单词 1/76: emperor
```

## 数据库结构

程序会自动创建SQLite数据库，包含以下主要字段：

- `unit_name`: 单元名称（从文件名提取）
- `word`: 单词原文
- `phonetic`: 音标
- `meaning`: 中文含义
- `difficulty`: 难度等级
- `theme`: 单词主题
- `ielts_frequency`: 雅思频率
- `learning_status`: 学习状态（默认为"待学习"）
- 其他详细字段...

## 错误处理

### 重试机制
- API调用失败时自动重试（默认最多5次）
- 数据库插入失败时重新获取单词信息
- 网络超时自动重试

### 进度保存
- 处理进度自动保存到 `processing_progress.json`
- 程序重启后自动跳过已处理的单词
- 支持断点续传

### 日志记录
- 所有操作都会记录到 `ielts_word_processor.log`
- 包含详细的错误信息和处理状态
- 支持不同日志级别（INFO, WARNING, ERROR）

## 常见问题

### 1. API调用失败
- 检查API密钥是否正确
- 确认网络连接正常
- 查看日志文件中的详细错误信息

### 2. 数据库约束错误
- 检查AI返回的数据格式是否符合要求
- 查看数据库设计文档中的约束条件
- 程序会自动重试获取正确格式的数据

### 3. 单词提取失败
- 检查输入文件格式是否正确
- 确认文件编码为UTF-8
- 查看日志中的具体错误信息

## 性能优化

- 合理设置 `request_delay` 避免API限流
- 使用 `max_retries` 控制重试次数
- 大文件处理时建议分批进行

## 许可证

本项目仅供学习和研究使用。
