-- 更新learning_tags字段，为每个标签添加emoji图标
-- SQLite批量更新语句

-- 备份原始数据（可选，建议执行前先备份）
-- CREATE TABLE ielts_words_backup AS SELECT * FROM ielts_words;

-- 使用REPLACE函数逐步替换每个标签
UPDATE ielts_words 
SET learning_tags = REPLACE(
    REPLACE(
        REPLACE(
            REPLACE(learning_tags, '写作', '✏️写作'),
            '听力', '🎧听力'
        ),
        '口语', '💬口语'
    ),
    '阅读', '📖阅读'
),
updated_at = CURRENT_TIMESTAMP;

-- 验证更新结果的查询语句
-- 查看更新后的learning_tags字段样例
SELECT 
    word, 
    learning_tags,
    updated_at
FROM ielts_words 
LIMIT 10;

-- 统计各种标签组合的数量
SELECT 
    learning_tags,
    COUNT(*) as count
FROM ielts_words 
GROUP BY learning_tags
ORDER BY count DESC;

-- 检查是否还有未更新的标签（不包含emoji的）
SELECT 
    word,
    learning_tags
FROM ielts_words 
WHERE learning_tags NOT LIKE '%✏️%' 
   AND learning_tags NOT LIKE '%🎧%' 
   AND learning_tags NOT LIKE '%💬%' 
   AND learning_tags NOT LIKE '%📖%'
   AND learning_tags != '';

-- 如果需要回滚更新（紧急情况下使用）
/*
UPDATE ielts_words 
SET learning_tags = REPLACE(
    REPLACE(
        REPLACE(
            REPLACE(learning_tags, '✏️写作', '写作'),
            '🎧听力', '听力'
        ),
        '💬口语', '口语'
    ),
    '📖阅读', '阅读'
),
updated_at = CURRENT_TIMESTAMP;
*/
