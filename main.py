#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
雅思单词批量处理程序 - 主程序
"""

import time
import sys
from typing import List

from config import Config
from logger import get_logger
from word_extractor import WordExtractor
from ai_client import AIClient
from database_manager import DatabaseManager

class WordProcessor:
    """单词处理器"""

    def __init__(self, config: Config):
        self.config = config
        self.logger = get_logger()
        self.word_extractor = WordExtractor()
        self.ai_client = AIClient(config)
        self.db_manager = DatabaseManager(config)
    
    def process_word_file(self, file_path: str) -> bool:
        """处理单个单词文件"""
        try:
            self.logger.info(f"开始处理文件: {file_path}")

            # 提取单词列表
            unit_name, words = self.word_extractor.load_words_from_file(file_path)
            if not unit_name or not words:
                self.logger.error(f"无法从文件提取单词: {file_path}")
                return False

            # 获取数据库中已处理的单词
            processed_words = set(self.db_manager.get_processed_words(unit_name))

            # 过滤出需要处理的单词
            words_to_process = [word for word in words if word not in processed_words]

            if not words_to_process:
                self.logger.info(f"单元 {unit_name} 的所有单词都已处理完成")
                return True

            self.logger.info(f"单元 {unit_name}: 总计 {len(words)} 个单词，需要处理 {len(words_to_process)} 个")

            # 处理单词
            success_count = 0
            failed_count = 0

            for i, word in enumerate(words_to_process, 1):
                self.logger.info(f"处理单词 {i}/{len(words_to_process)}: {word}")

                # 智能重试处理单词
                if self._process_single_word_with_retry(unit_name, word):
                    success_count += 1
                    self.logger.info(f"成功处理单词: {word}")
                else:
                    failed_count += 1
                    self.logger.error(f"处理单词失败: {word}")

                # 请求间隔
                if i < len(words_to_process):
                    time.sleep(self.config.request_delay)

            # 输出统计信息
            self.logger.info(f"单元 {unit_name} 处理完成:")
            self.logger.info(f"  成功处理: {success_count} 个单词")
            self.logger.info(f"  失败: {failed_count} 个单词")

            # 显示单元统计
            stats = self.db_manager.get_unit_statistics(unit_name)
            if stats:
                self.logger.info(f"  单元统计: 总计 {stats['total_words']} 个单词")

            return success_count > 0

        except Exception as e:
            self.logger.error(f"处理文件时发生错误 {file_path}: {e}")
            return False

    def _process_single_word_with_retry(self, unit_name: str, word: str) -> bool:
        """智能重试处理单个单词"""
        error_context = None

        for attempt in range(self.config.max_retries):
            try:
                # 获取单词信息，包含错误上下文
                word_data = self.ai_client.get_word_info(word, error_context)
                if not word_data:
                    self.logger.error(f"获取单词信息失败: {word} (尝试 {attempt + 1}/{self.config.max_retries})")
                    continue

                # 尝试存储到数据库
                success, error_msg = self.db_manager.insert_word(unit_name, word_data)
                if success:
                    return True
                else:
                    # 数据库插入失败，将错误信息作为上下文用于下次重试
                    error_context = error_msg
                    self.logger.error(f"数据库插入失败: {word} (尝试 {attempt + 1}/{self.config.max_retries}) - {error_msg}")

                    # 如果不是最后一次尝试，等待后重试
                    if attempt < self.config.max_retries - 1:
                        self.logger.info(f"将在 {self.config.request_delay} 秒后重试...")
                        time.sleep(self.config.request_delay)

            except Exception as e:
                self.logger.error(f"处理单词时发生异常: {word} (尝试 {attempt + 1}/{self.config.max_retries}) - {e}")
                error_context = f"处理异常: {str(e)}"

                if attempt < self.config.max_retries - 1:
                    time.sleep(self.config.request_delay)

        self.logger.error(f"单词处理失败，已达到最大重试次数: {word}")
        return False
    
    def process_all_files(self, file_paths: List[str] = None) -> bool:
        """处理所有单词文件"""
        try:
            if file_paths is None:
                file_paths = self.word_extractor.get_available_word_files()
            
            if not file_paths:
                self.logger.error("没有找到可处理的单词文件")
                return False
            
            self.logger.info(f"找到 {len(file_paths)} 个单词文件")
            
            total_success = 0
            for i, file_path in enumerate(file_paths, 1):
                self.logger.info(f"处理文件 {i}/{len(file_paths)}: {file_path}")
                
                if self.process_word_file(file_path):
                    total_success += 1
                
                # 文件间隔
                if i < len(file_paths):
                    time.sleep(2)
            
            # 显示总体统计
            overall_stats = self.db_manager.get_overall_statistics()
            self.logger.info("=" * 50)
            self.logger.info("处理完成，总体统计:")
            self.logger.info(f"  成功处理文件: {total_success}/{len(file_paths)}")
            self.logger.info(f"  总单词数: {overall_stats['total_words']}")
            self.logger.info(f"  总单元数: {overall_stats['total_units']}")
            self.logger.info(f"  学习完成率: {overall_stats['completion_rate']}%")
            
            return total_success > 0
            
        except Exception as e:
            self.logger.error(f"批量处理时发生错误: {e}")
            return False

def ask_for_csv_export() -> bool:
    """询问用户是否需要导出CSV"""
    while True:
        try:
            response = input("是否需要导出现有数据到CSV文件？(y/n): ").strip().lower()
            if response in ['y', 'yes', '是', '1']:
                return True
            elif response in ['n', 'no', '否', '0']:
                return False
            else:
                print("请输入 y/yes/是/1 表示是，或 n/no/否/0 表示否")
        except KeyboardInterrupt:
            print("\n程序被用户中断")
            return False
        except Exception:
            print("输入无效，请重新输入")

def main():
    """主函数"""
    try:
        # 加载配置
        config = Config.from_env()
        config.validate()

        # 初始化日志，设置为DEBUG级别以便看到详细信息
        logger = get_logger(config.log_file, "DEBUG")
        logger.info("=" * 50)
        logger.info("雅思单词批量处理程序启动")
        logger.info(f"配置信息:")
        logger.info(f"  模型: {config.model_name}")
        logger.info(f"  数据库: {config.db_path}")
        logger.info(f"  最大重试次数: {config.max_retries}")
        logger.info(f"  请求间隔: {config.request_delay}秒")

        # 创建处理器
        processor = WordProcessor(config)

        # 检查数据库中是否有数据，如果有则询问是否导出CSV
        stats = processor.db_manager.get_overall_statistics()
        if stats['total_words'] > 0:
            print(f"\n数据库中已有 {stats['total_words']} 个单词，分布在 {stats['total_units']} 个单元中")
            
            if ask_for_csv_export():
                print("正在导出CSV文件...")
                csv_filename = f"ielts_words_export_{time.strftime('%Y%m%d_%H%M%S')}.csv"

                if processor.db_manager.export_to_csv(csv_filename):
                    print(f"✓ 成功导出到文件: {csv_filename}")

                    # 显示导出统计
                    export_stats = processor.db_manager.get_export_statistics()
                    print(f"导出统计:")
                    print(f"  总单词数: {export_stats['total_words']}")
                    for unit_info in export_stats['units']:
                        print(f"  单元 {unit_info['unit_name']}: {unit_info['word_count']} 个单词")
                else:
                    print("✗ CSV导出失败，请查看日志文件")

                print()  # 空行分隔
           
        # 处理命令行参数
        if len(sys.argv) > 1:
            # 处理指定的文件
            file_paths = sys.argv[1:]
            logger.info(f"处理指定文件: {file_paths}")
            success = processor.process_all_files(file_paths)
        else:
            # 处理所有文件
            logger.info("处理所有可用的单词文件")
            success = processor.process_all_files()

        if success:
            logger.info("程序执行成功")
            return 0
        else:
            logger.error("程序执行失败")
            return 1

    except Exception as e:
        logger = get_logger()
        logger.error(f"程序执行时发生严重错误: {e}")
        logger.exception("详细错误信息:")
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
